'use client';

import { useEffect, useRef } from 'react';

interface KeyboardActions {
  moveLeft: () => void;
  moveRight: () => void;
  moveDown: () => void;
  rotate: () => void;
  hardDrop: () => void;
  pause: () => void;
  resume: () => void;
  restart: () => void;
}

interface UseKeyboardProps {
  actions: KeyboardActions;
  gameOver: boolean;
  paused: boolean;
}

export function useKeyboard({ actions, gameOver, paused }: UseKeyboardProps) {
  const keysPressed = useRef<Set<string>>(new Set());
  const lastMoveTime = useRef<{ [key: string]: number }>({});

  const REPEAT_DELAY = 80; // milliseconds - mais rápido para movimento fluido
  const INITIAL_DELAY = 120; // milliseconds - delay inicial menor
  const SOFT_DROP_DELAY = 50; // milliseconds - queda suave mais rápida

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const key = event.key.toLowerCase();
      const now = Date.now();
      
      // Prevent default for game keys
      if (['arrowleft', 'arrowright', 'arrowdown', 'arrowup', ' ', 'p', 'r'].includes(key)) {
        event.preventDefault();
      }

      // Handle single-press keys
      if (!keysPressed.current.has(key)) {
        keysPressed.current.add(key);
        lastMoveTime.current[key] = now;

        switch (key) {
          case 'arrowup':
            if (!gameOver && !paused) {
              actions.rotate();
            }
            break;
          case ' ':
            if (!gameOver && !paused) {
              actions.hardDrop();
            }
            break;
          case 'p':
            if (!gameOver) {
              if (paused) {
                actions.resume();
              } else {
                actions.pause();
              }
            }
            break;
          case 'r':
            actions.restart();
            break;
        }
      }

      // Handle repeating keys
      if (keysPressed.current.has(key)) {
        const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);
        let delay = REPEAT_DELAY;

        // Diferentes delays para diferentes tipos de movimento
        if (key === 'arrowdown') {
          delay = SOFT_DROP_DELAY; // Queda suave mais rápida
        } else if (lastMoveTime.current[key] === now) {
          delay = INITIAL_DELAY; // Delay inicial para movimento lateral
        }

        if (timeSinceLastMove >= delay) {
          lastMoveTime.current[key] = now;

          switch (key) {
            case 'arrowleft':
              if (!gameOver && !paused) {
                actions.moveLeft();
              }
              break;
            case 'arrowright':
              if (!gameOver && !paused) {
                actions.moveRight();
              }
              break;
            case 'arrowdown':
              if (!gameOver && !paused) {
                actions.moveDown();
              }
              break;
          }
        }
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      const key = event.key.toLowerCase();
      keysPressed.current.delete(key);
      delete lastMoveTime.current[key];
    };

    // Handle repeating movement
    const handleRepeat = () => {
      const now = Date.now();

      keysPressed.current.forEach(key => {
        const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);
        let delay = REPEAT_DELAY;

        // Diferentes delays para diferentes tipos de movimento
        if (key === 'arrowdown') {
          delay = SOFT_DROP_DELAY;
        }

        if (timeSinceLastMove >= delay) {
          lastMoveTime.current[key] = now;

          switch (key) {
            case 'arrowleft':
              if (!gameOver && !paused) {
                actions.moveLeft();
              }
              break;
            case 'arrowright':
              if (!gameOver && !paused) {
                actions.moveRight();
              }
              break;
            case 'arrowdown':
              if (!gameOver && !paused) {
                actions.moveDown();
              }
              break;
          }
        }
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    const intervalId = setInterval(handleRepeat, 16); // ~60fps para movimento mais suave

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      clearInterval(intervalId);
    };
  }, [actions, gameOver, paused]);
}
