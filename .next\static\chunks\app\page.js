/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=false!":
/*!***************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=false! ***!
  \***************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(app-pages-browser)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1EJTNBJTVDVmlzaW9ubmFpcmUtUmVwR2l0JTVDVGV0cmlzLVByb2plY3QlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/MzA1MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFZpc2lvbm5haXJlLVJlcEdpdFxcXFxUZXRyaXMtUHJvamVjdFxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_TetrisGame__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TetrisGame */ \"(app-pages-browser)/./components/TetrisGame.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TetrisGame__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRWlEO0FBRWxDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0YsOERBQVVBOzs7Ozs7Ozs7O0FBR2pCO0tBTndCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBUZXRyaXNHYW1lIGZyb20gJ0AvY29tcG9uZW50cy9UZXRyaXNHYW1lJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbj5cbiAgICAgIDxUZXRyaXNHYW1lIC8+XG4gICAgPC9tYWluPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRldHJpc0dhbWUiLCJIb21lIiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/Board.tsx":
/*!******************************!*\
  !*** ./components/Board.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Board; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(app-pages-browser)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Board(param) {\n    let { board, currentPiece, ghostPiece } = param;\n    // Create a display board that includes the current piece\n    const displayBoard = board.map((row)=>row.map((cell)=>({\n                ...cell\n            })));\n    // Add ghost piece to display board\n    if (ghostPiece) {\n        const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[ghostPiece.type][ghostPiece.rotation];\n        for(let y = 0; y < shape.length; y++){\n            for(let x = 0; x < shape[y].length; x++){\n                if (shape[y][x]) {\n                    const boardX = ghostPiece.position.x + x;\n                    const boardY = ghostPiece.position.y + y;\n                    if (boardY >= 0 && boardY < displayBoard.length && boardX >= 0 && boardX < displayBoard[0].length && !displayBoard[boardY][boardX].filled) {\n                        displayBoard[boardY][boardX] = {\n                            filled: true,\n                            type: ghostPiece.type,\n                            isGhost: true\n                        };\n                    }\n                }\n            }\n        }\n    }\n    // Add current piece to display board\n    if (currentPiece) {\n        const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[currentPiece.type][currentPiece.rotation];\n        for(let y = 0; y < shape.length; y++){\n            for(let x = 0; x < shape[y].length; x++){\n                if (shape[y][x]) {\n                    const boardX = currentPiece.position.x + x;\n                    const boardY = currentPiece.position.y + y;\n                    if (boardY >= 0 && boardY < displayBoard.length && boardX >= 0 && boardX < displayBoard[0].length) {\n                        displayBoard[boardY][boardX] = {\n                            filled: true,\n                            type: currentPiece.type\n                        };\n                    }\n                }\n            }\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"tetris-board\",\n        children: displayBoard.map((row, y)=>row.map((cell, x)=>{\n                var _cell_type, _cell_type1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"tetris-cell \".concat(cell.filled ? \"tetris-cell-filled \".concat(cell.isGhost ? \"tetris-piece-\".concat((_cell_type = cell.type) === null || _cell_type === void 0 ? void 0 : _cell_type.toLowerCase(), \" opacity-40 border-dashed\") : \"tetris-piece-\".concat((_cell_type1 = cell.type) === null || _cell_type1 === void 0 ? void 0 : _cell_type1.toLowerCase())) : \"\")\n                }, \"\".concat(x, \"-\").concat(y), false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\Board.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this);\n            }))\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\Board.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = Board;\nvar _c;\n$RefreshReg$(_c, \"Board\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/Board.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/GameControls.tsx":
/*!*************************************!*\
  !*** ./components/GameControls.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GameControls; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GameControls(param) {\n    let { gameOver, paused, onStart, onPause, onResume, onRestart } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-3\",\n                children: \"Controls\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: gameOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onStart,\n                    className: \"game-button w-full\",\n                    children: \"New Game\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        paused ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onResume,\n                            className: \"game-button w-full\",\n                            children: \"Resume\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onPause,\n                            className: \"game-button w-full\",\n                            children: \"Pause\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRestart,\n                            className: \"game-button w-full bg-red-600 hover:bg-red-700\",\n                            children: \"Restart\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-300 space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold mb-2\",\n                        children: \"Keyboard Controls:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"← → : Move left/right\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"↓ : Soft drop\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"↑ : Rotate\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Space : Hard drop\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"P : Pause/Resume\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"R : Restart\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = GameControls;\nvar _c;\n$RefreshReg$(_c, \"GameControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/GameControls.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/GameStats.tsx":
/*!**********************************!*\
  !*** ./components/GameStats.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GameStats; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GameStats(param) {\n    let { stats } = param;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins.toString().padStart(2, \"0\"), \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-3\",\n                children: \"Statistics\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Score:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-yellow-400\",\n                                children: stats.score.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Level:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-blue-400\",\n                                children: stats.level\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Lines:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-green-400\",\n                                children: stats.lines\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Time:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-purple-400\",\n                                children: formatTime(stats.time)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_c = GameStats;\nvar _c;\n$RefreshReg$(_c, \"GameStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvR2FtZVN0YXRzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUwQjtBQU9YLFNBQVNDLFVBQVUsS0FBeUI7UUFBekIsRUFBRUMsS0FBSyxFQUFrQixHQUF6QjtJQUNoQyxNQUFNQyxhQUFhLENBQUNDO1FBQ2xCLE1BQU1DLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0gsVUFBVTtRQUNsQyxNQUFNSSxPQUFPSixVQUFVO1FBQ3ZCLE9BQU8sR0FBdUNJLE9BQXBDSCxLQUFLSSxRQUFRLEdBQUdDLFFBQVEsQ0FBQyxHQUFHLE1BQUssS0FBb0MsT0FBakNGLEtBQUtDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUc7SUFDNUU7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUF5Qjs7Ozs7OzBCQUV2Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFLRixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQTZCVixNQUFNYSxLQUFLLENBQUNDLGNBQWM7Ozs7Ozs7Ozs7OztrQ0FHekUsOERBQUNMO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQWdCOzs7Ozs7MENBQ2hDLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBMkJWLE1BQU1lLEtBQUs7Ozs7Ozs7Ozs7OztrQ0FHeEQsOERBQUNOO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQWdCOzs7Ozs7MENBQ2hDLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBNEJWLE1BQU1nQixLQUFLOzs7Ozs7Ozs7Ozs7a0NBR3pELDhEQUFDUDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFLRixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQTZCVCxXQUFXRCxNQUFNaUIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzVFO0tBbEN3QmxCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvR2FtZVN0YXRzLnRzeD8xMzM0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEdhbWVTdGF0cyBhcyBHYW1lU3RhdHNUeXBlIH0gZnJvbSAnQC90eXBlcy90ZXRyaXMnO1xuXG5pbnRlcmZhY2UgR2FtZVN0YXRzUHJvcHMge1xuICBzdGF0czogR2FtZVN0YXRzVHlwZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2FtZVN0YXRzKHsgc3RhdHMgfTogR2FtZVN0YXRzUHJvcHMpIHtcbiAgY29uc3QgZm9ybWF0VGltZSA9IChzZWNvbmRzOiBudW1iZXIpOiBzdHJpbmcgPT4ge1xuICAgIGNvbnN0IG1pbnMgPSBNYXRoLmZsb29yKHNlY29uZHMgLyA2MCk7XG4gICAgY29uc3Qgc2VjcyA9IHNlY29uZHMgJSA2MDtcbiAgICByZXR1cm4gYCR7bWlucy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9OiR7c2Vjcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAgcC00IHJvdW5kZWQtbGcgc3BhY2UteS0zXCI+XG4gICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItM1wiPlN0YXRpc3RpY3M8L2gzPlxuICAgICAgXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlNjb3JlOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC15ZWxsb3ctNDAwXCI+e3N0YXRzLnNjb3JlLnRvTG9jYWxlU3RyaW5nKCl9PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+TGV2ZWw6PC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LWJsdWUtNDAwXCI+e3N0YXRzLmxldmVsfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPkxpbmVzOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1ncmVlbi00MDBcIj57c3RhdHMubGluZXN9PC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+VGltZTo8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtcHVycGxlLTQwMFwiPntmb3JtYXRUaW1lKHN0YXRzLnRpbWUpfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkdhbWVTdGF0cyIsInN0YXRzIiwiZm9ybWF0VGltZSIsInNlY29uZHMiLCJtaW5zIiwiTWF0aCIsImZsb29yIiwic2VjcyIsInRvU3RyaW5nIiwicGFkU3RhcnQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsInNwYW4iLCJzY29yZSIsInRvTG9jYWxlU3RyaW5nIiwibGV2ZWwiLCJsaW5lcyIsInRpbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/GameStats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/NextPiece.tsx":
/*!**********************************!*\
  !*** ./components/NextPiece.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NextPiece; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(app-pages-browser)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NextPiece(param) {\n    let { piece } = param;\n    var _shape_;\n    if (!piece) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-800 p-4 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold mb-2\",\n                    children: \"Next\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 bg-black border border-gray-600 rounded\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this);\n    }\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[piece.type][0]; // Always show first rotation\n    const maxSize = Math.max(shape.length, ((_shape_ = shape[0]) === null || _shape_ === void 0 ? void 0 : _shape_.length) || 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-2\",\n                children: \"Next\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-0 border border-gray-600 bg-black p-1 w-fit\",\n                style: {\n                    gridTemplateColumns: \"repeat(\".concat(maxSize, \", 1fr)\"),\n                    gridTemplateRows: \"repeat(\".concat(maxSize, \", 1fr)\")\n                },\n                children: Array(maxSize).fill(null).map((_, y)=>Array(maxSize).fill(null).map((_, x)=>{\n                        const isPartOfPiece = y < shape.length && x < shape[y].length && shape[y][x];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-4 h-4 border border-gray-700 \".concat(isPartOfPiece ? \"tetris-piece-\".concat(piece.type.toLowerCase()) : \"bg-black\")\n                        }, \"\".concat(x, \"-\").concat(y), false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, this);\n                    }))\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_c = NextPiece;\nvar _c;\n$RefreshReg$(_c, \"NextPiece\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/NextPiece.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/TetrisGame.tsx":
/*!***********************************!*\
  !*** ./components/TetrisGame.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TetrisGame; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTetris */ \"(app-pages-browser)/./hooks/useTetris.ts\");\n/* harmony import */ var _hooks_useKeyboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useKeyboard */ \"(app-pages-browser)/./hooks/useKeyboard.ts\");\n/* harmony import */ var _Board__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Board */ \"(app-pages-browser)/./components/Board.tsx\");\n/* harmony import */ var _NextPiece__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NextPiece */ \"(app-pages-browser)/./components/NextPiece.tsx\");\n/* harmony import */ var _GameStats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GameStats */ \"(app-pages-browser)/./components/GameStats.tsx\");\n/* harmony import */ var _GameControls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GameControls */ \"(app-pages-browser)/./components/GameControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TetrisGame() {\n    _s();\n    const { gameState, gameTime, ghostPiece, actions } = (0,_hooks_useTetris__WEBPACK_IMPORTED_MODULE_2__.useTetris)();\n    const keyboardActions = {\n        moveLeft: ()=>actions.movePiece(-1, 0),\n        moveRight: ()=>actions.movePiece(1, 0),\n        moveDown: ()=>actions.movePiece(0, 1),\n        rotate: actions.rotatePiece,\n        hardDrop: actions.hardDrop,\n        pause: actions.pauseGame,\n        resume: actions.resumeGame,\n        restart: actions.startNewGame\n    };\n    (0,_hooks_useKeyboard__WEBPACK_IMPORTED_MODULE_3__.useKeyboard)({\n        actions: keyboardActions,\n        gameOver: gameState.gameOver,\n        paused: gameState.paused\n    });\n    const gameStats = {\n        score: gameState.score,\n        level: gameState.level,\n        lines: gameState.lines,\n        time: gameTime\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center mb-8 text-white\",\n                    children: \"Tetris\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NextPiece__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    piece: gameState.nextPiece\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GameStats__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    stats: gameStats\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Board__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            board: gameState.board,\n                                            currentPiece: gameState.currentPiece,\n                                            ghostPiece: ghostPiece\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        !gameState.currentPiece && !gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Tetris\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-4\",\n                                                        children: \"Ready to play?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.startNewGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Start Game\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this),\n                                        gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Game Over\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl mb-4\",\n                                                        children: [\n                                                            \"Score: \",\n                                                            gameState.score.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.startNewGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Play Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        gameState.paused && !gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Paused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.resumeGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Resume\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden grid grid-cols-3 gap-2 w-full max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(-1, 0),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"←\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: actions.rotatePiece,\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"↻\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(1, 0),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(0, 1),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: actions.hardDrop,\n                                            className: \"game-button py-3 bg-red-600 hover:bg-red-700\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"Drop\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: gameState.paused ? actions.resumeGame : actions.pauseGame,\n                                            className: \"game-button py-3 bg-yellow-600 hover:bg-yellow-700\",\n                                            disabled: gameState.gameOver,\n                                            children: gameState.paused ? \"▶\" : \"⏸\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GameControls__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                gameOver: gameState.gameOver,\n                                paused: gameState.paused,\n                                onStart: actions.startNewGame,\n                                onPause: actions.pauseGame,\n                                onResume: actions.resumeGame,\n                                onRestart: actions.startNewGame\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center text-gray-300 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Use arrow keys to move and rotate pieces. Space for hard drop, P to pause, R to restart.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(TetrisGame, \"Bu3E1NNHT1tXPFv3CrVdCPK9pBc=\", false, function() {\n    return [\n        _hooks_useTetris__WEBPACK_IMPORTED_MODULE_2__.useTetris,\n        _hooks_useKeyboard__WEBPACK_IMPORTED_MODULE_3__.useKeyboard\n    ];\n});\n_c = TetrisGame;\nvar _c;\n$RefreshReg$(_c, \"TetrisGame\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/TetrisGame.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./constants/tetris.ts":
/*!*****************************!*\
  !*** ./constants/tetris.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOARD_HEIGHT: function() { return /* binding */ BOARD_HEIGHT; },\n/* harmony export */   BOARD_WIDTH: function() { return /* binding */ BOARD_WIDTH; },\n/* harmony export */   INITIAL_DROP_TIME: function() { return /* binding */ INITIAL_DROP_TIME; },\n/* harmony export */   LEVEL_SPEED_INCREASE: function() { return /* binding */ LEVEL_SPEED_INCREASE; },\n/* harmony export */   LINES_PER_LEVEL: function() { return /* binding */ LINES_PER_LEVEL; },\n/* harmony export */   PIECE_SHAPES: function() { return /* binding */ PIECE_SHAPES; },\n/* harmony export */   PIECE_TYPES: function() { return /* binding */ PIECE_TYPES; },\n/* harmony export */   POINTS: function() { return /* binding */ POINTS; }\n/* harmony export */ });\nconst BOARD_WIDTH = 10;\nconst BOARD_HEIGHT = 20;\nconst PIECE_SHAPES = {\n    I: [\n        [\n            [\n                0,\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0,\n                0\n            ],\n            [\n                0,\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    O: [\n        [\n            [\n                1,\n                1\n            ],\n            [\n                1,\n                1\n            ]\n        ]\n    ],\n    T: [\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    S: [\n        [\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                1\n            ]\n        ]\n    ],\n    Z: [\n        [\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                1\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    J: [\n        [\n            [\n                1,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                1\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                0\n            ]\n        ]\n    ],\n    L: [\n        [\n            [\n                0,\n                0,\n                1\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                1,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ]\n};\nconst PIECE_TYPES = [\n    \"I\",\n    \"O\",\n    \"T\",\n    \"S\",\n    \"Z\",\n    \"J\",\n    \"L\"\n];\nconst INITIAL_DROP_TIME = 1000; // milliseconds\nconst LEVEL_SPEED_INCREASE = 0.9; // multiply drop time by this each level\nconst POINTS = {\n    SINGLE: 100,\n    DOUBLE: 300,\n    TRIPLE: 500,\n    TETRIS: 800,\n    SOFT_DROP: 1,\n    HARD_DROP: 2\n};\nconst LINES_PER_LEVEL = 10;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./constants/tetris.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useKeyboard.ts":
/*!******************************!*\
  !*** ./hooks/useKeyboard.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useKeyboard: function() { return /* binding */ useKeyboard; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useKeyboard auto */ \nfunction useKeyboard(param) {\n    let { actions, gameOver, paused } = param;\n    const keysPressed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Set());\n    const lastMoveTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const REPEAT_DELAY = 80; // milliseconds - mais rápido para movimento fluido\n    const INITIAL_DELAY = 120; // milliseconds - delay inicial menor\n    const SOFT_DROP_DELAY = 50; // milliseconds - queda suave mais rápida\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            const key = event.key.toLowerCase();\n            const now = Date.now();\n            // Prevent default for game keys\n            if ([\n                \"arrowleft\",\n                \"arrowright\",\n                \"arrowdown\",\n                \"arrowup\",\n                \" \",\n                \"p\",\n                \"r\"\n            ].includes(key)) {\n                event.preventDefault();\n            }\n            // Handle single-press keys\n            if (!keysPressed.current.has(key)) {\n                keysPressed.current.add(key);\n                lastMoveTime.current[key] = now;\n                switch(key){\n                    case \"arrowup\":\n                        if (!gameOver && !paused) {\n                            actions.rotate();\n                        }\n                        break;\n                    case \" \":\n                        if (!gameOver && !paused) {\n                            actions.hardDrop();\n                        }\n                        break;\n                    case \"p\":\n                        if (!gameOver) {\n                            if (paused) {\n                                actions.resume();\n                            } else {\n                                actions.pause();\n                            }\n                        }\n                        break;\n                    case \"r\":\n                        actions.restart();\n                        break;\n                }\n            }\n            // Handle repeating keys\n            if (keysPressed.current.has(key)) {\n                const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);\n                let delay = REPEAT_DELAY;\n                // Diferentes delays para diferentes tipos de movimento\n                if (key === \"arrowdown\") {\n                    delay = SOFT_DROP_DELAY; // Queda suave mais rápida\n                } else if (lastMoveTime.current[key] === now) {\n                    delay = INITIAL_DELAY; // Delay inicial para movimento lateral\n                }\n                if (timeSinceLastMove >= delay) {\n                    lastMoveTime.current[key] = now;\n                    switch(key){\n                        case \"arrowleft\":\n                            if (!gameOver && !paused) {\n                                actions.moveLeft();\n                            }\n                            break;\n                        case \"arrowright\":\n                            if (!gameOver && !paused) {\n                                actions.moveRight();\n                            }\n                            break;\n                        case \"arrowdown\":\n                            if (!gameOver && !paused) {\n                                actions.moveDown();\n                            }\n                            break;\n                    }\n                }\n            }\n        };\n        const handleKeyUp = (event)=>{\n            const key = event.key.toLowerCase();\n            keysPressed.current.delete(key);\n            delete lastMoveTime.current[key];\n        };\n        // Handle repeating movement\n        const handleRepeat = ()=>{\n            const now = Date.now();\n            keysPressed.current.forEach((key)=>{\n                const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);\n                let delay = REPEAT_DELAY;\n                // Diferentes delays para diferentes tipos de movimento\n                if (key === \"arrowdown\") {\n                    delay = SOFT_DROP_DELAY;\n                }\n                if (timeSinceLastMove >= delay) {\n                    lastMoveTime.current[key] = now;\n                    switch(key){\n                        case \"arrowleft\":\n                            if (!gameOver && !paused) {\n                                actions.moveLeft();\n                            }\n                            break;\n                        case \"arrowright\":\n                            if (!gameOver && !paused) {\n                                actions.moveRight();\n                            }\n                            break;\n                        case \"arrowdown\":\n                            if (!gameOver && !paused) {\n                                actions.moveDown();\n                            }\n                            break;\n                    }\n                }\n            });\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        window.addEventListener(\"keyup\", handleKeyUp);\n        const intervalId = setInterval(handleRepeat, 16); // ~60fps para movimento mais suave\n        return ()=>{\n            window.removeEventListener(\"keydown\", handleKeyDown);\n            window.removeEventListener(\"keyup\", handleKeyUp);\n            clearInterval(intervalId);\n        };\n    }, [\n        actions,\n        gameOver,\n        paused\n    ]);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useKeyboard.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/useTetris.ts":
/*!****************************!*\
  !*** ./hooks/useTetris.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTetris: function() { return /* binding */ useTetris; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_tetris__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/tetris */ \"(app-pages-browser)/./utils/tetris.ts\");\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(app-pages-browser)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ useTetris auto */ \n\n\nfunction useTetris() {\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        board: (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createEmptyBoard)(),\n        currentPiece: null,\n        nextPiece: null,\n        score: 0,\n        level: 1,\n        lines: 0,\n        gameOver: false,\n        paused: false\n    });\n    const [gameTime, setGameTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const dropTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(_constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME);\n    const lastDropRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const gameLoopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const timeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    // Initialize game on first load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!gameState.currentPiece && !gameState.gameOver) {\n            const firstPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            const secondPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            setGameState((prev)=>({\n                    ...prev,\n                    currentPiece: firstPiece,\n                    nextPiece: secondPiece\n                }));\n        }\n    }, []);\n    const calculateDropTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((level)=>{\n        return Math.max(50, _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME * Math.pow(_constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LEVEL_SPEED_INCREASE, level - 1));\n    }, []);\n    const startNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const firstPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n        const secondPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n        setGameState({\n            board: (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createEmptyBoard)(),\n            currentPiece: firstPiece,\n            nextPiece: secondPiece,\n            score: 0,\n            level: 1,\n            lines: 0,\n            gameOver: false,\n            paused: false\n        });\n        setGameTime(0);\n        dropTimeRef.current = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME;\n        lastDropRef.current = Date.now();\n    }, []);\n    const pauseGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>({\n                ...prev,\n                paused: true\n            }));\n    }, []);\n    const resumeGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>({\n                ...prev,\n                paused: false\n            }));\n        lastDropRef.current = Date.now();\n    }, []);\n    const movePiece = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dx, dy)=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const newPosition = {\n                x: prev.currentPiece.position.x + dx,\n                y: prev.currentPiece.position.y + dy\n            };\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, newPosition)) {\n                return {\n                    ...prev,\n                    currentPiece: {\n                        ...prev.currentPiece,\n                        position: newPosition\n                    }\n                };\n            }\n            return prev;\n        });\n    }, []);\n    const rotatePieceAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const rotatedPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.rotatePiece)(prev.currentPiece);\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, rotatedPiece, rotatedPiece.position)) {\n                return {\n                    ...prev,\n                    currentPiece: rotatedPiece\n                };\n            }\n            return prev;\n        });\n    }, []);\n    const hardDrop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            let dropDistance = 0;\n            let newY = prev.currentPiece.position.y;\n            while((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, {\n                x: prev.currentPiece.position.x,\n                y: newY + 1\n            })){\n                newY++;\n                dropDistance++;\n            }\n            const droppedPiece = {\n                ...prev.currentPiece,\n                position: {\n                    x: prev.currentPiece.position.x,\n                    y: newY\n                }\n            };\n            const newBoard = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.placePiece)(prev.board, droppedPiece);\n            const { newBoard: clearedBoard, linesCleared } = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.clearLines)(newBoard);\n            const newLines = prev.lines + linesCleared;\n            const newLevel = Math.floor(newLines / _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LINES_PER_LEVEL) + 1;\n            let scoreIncrease = dropDistance * _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.HARD_DROP;\n            if (linesCleared > 0) {\n                const linePoints = [\n                    0,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.SINGLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.DOUBLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TRIPLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TETRIS\n                ];\n                scoreIncrease += linePoints[linesCleared] * newLevel;\n            }\n            const newPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            const gameOver = !(0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(clearedBoard, prev.nextPiece, prev.nextPiece.position);\n            return {\n                ...prev,\n                board: clearedBoard,\n                currentPiece: gameOver ? null : prev.nextPiece,\n                nextPiece: gameOver ? null : newPiece,\n                score: prev.score + scoreIncrease,\n                level: newLevel,\n                lines: newLines,\n                gameOver\n            };\n        });\n    }, []);\n    const dropPiece = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const newPosition = {\n                x: prev.currentPiece.position.x,\n                y: prev.currentPiece.position.y + 1\n            };\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, newPosition)) {\n                return {\n                    ...prev,\n                    currentPiece: {\n                        ...prev.currentPiece,\n                        position: newPosition\n                    }\n                };\n            } else {\n                // Piece has landed\n                const newBoard = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.placePiece)(prev.board, prev.currentPiece);\n                const { newBoard: clearedBoard, linesCleared } = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.clearLines)(newBoard);\n                const newLines = prev.lines + linesCleared;\n                const newLevel = Math.floor(newLines / _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LINES_PER_LEVEL) + 1;\n                let scoreIncrease = 0;\n                if (linesCleared > 0) {\n                    const linePoints = [\n                        0,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.SINGLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.DOUBLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TRIPLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TETRIS\n                    ];\n                    scoreIncrease = linePoints[linesCleared] * newLevel;\n                }\n                const newPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n                const gameOver = prev.nextPiece ? !(0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(clearedBoard, prev.nextPiece, prev.nextPiece.position) : true;\n                return {\n                    ...prev,\n                    board: clearedBoard,\n                    currentPiece: gameOver ? null : prev.nextPiece,\n                    nextPiece: gameOver ? null : newPiece,\n                    score: prev.score + scoreIncrease,\n                    level: newLevel,\n                    lines: newLines,\n                    gameOver\n                };\n            }\n        });\n    }, []);\n    // Game loop\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (gameState.gameOver || gameState.paused || !gameState.currentPiece) {\n            if (gameLoopRef.current) {\n                cancelAnimationFrame(gameLoopRef.current);\n            }\n            return;\n        }\n        const gameLoop = ()=>{\n            const now = Date.now();\n            const dropTime = calculateDropTime(gameState.level);\n            if (now - lastDropRef.current > dropTime) {\n                dropPiece();\n                lastDropRef.current = now;\n            }\n            gameLoopRef.current = requestAnimationFrame(gameLoop);\n        };\n        gameLoopRef.current = requestAnimationFrame(gameLoop);\n        return ()=>{\n            if (gameLoopRef.current) {\n                cancelAnimationFrame(gameLoopRef.current);\n            }\n        };\n    }, [\n        gameState.gameOver,\n        gameState.paused,\n        gameState.currentPiece,\n        gameState.level,\n        dropPiece,\n        calculateDropTime\n    ]);\n    // Timer\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (gameState.gameOver || gameState.paused) {\n            if (timeRef.current) {\n                clearInterval(timeRef.current);\n            }\n            return;\n        }\n        timeRef.current = window.setInterval(()=>{\n            setGameTime((prev)=>prev + 1);\n        }, 1000);\n        return ()=>{\n            if (timeRef.current) {\n                clearInterval(timeRef.current);\n            }\n        };\n    }, [\n        gameState.gameOver,\n        gameState.paused\n    ]);\n    const ghostPiece = gameState.currentPiece ? (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.getGhostPiece)(gameState.board, gameState.currentPiece) : null;\n    return {\n        gameState,\n        gameTime,\n        ghostPiece,\n        actions: {\n            startNewGame,\n            pauseGame,\n            resumeGame,\n            movePiece,\n            rotatePiece: rotatePieceAction,\n            hardDrop,\n            dropPiece\n        }\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useTetris.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./utils/tetris.ts":
/*!*************************!*\
  !*** ./utils/tetris.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearLines: function() { return /* binding */ clearLines; },\n/* harmony export */   createEmptyBoard: function() { return /* binding */ createEmptyBoard; },\n/* harmony export */   createRandomPiece: function() { return /* binding */ createRandomPiece; },\n/* harmony export */   getGhostPiece: function() { return /* binding */ getGhostPiece; },\n/* harmony export */   isValidPosition: function() { return /* binding */ isValidPosition; },\n/* harmony export */   placePiece: function() { return /* binding */ placePiece; },\n/* harmony export */   rotatePiece: function() { return /* binding */ rotatePiece; }\n/* harmony export */ });\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants/tetris */ \"(app-pages-browser)/./constants/tetris.ts\");\n\nfunction createEmptyBoard() {\n    return Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT).fill(null).map(()=>Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH).fill(null).map(()=>({\n                filled: false\n            })));\n}\nfunction createRandomPiece() {\n    const type = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_TYPES[Math.floor(Math.random() * _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_TYPES.length)];\n    return {\n        type,\n        shape: _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[type][0],\n        position: {\n            x: Math.floor(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH / 2) - 1,\n            y: 0\n        },\n        rotation: 0\n    };\n}\nfunction isValidPosition(board, piece, position) {\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type][piece.rotation];\n    for(let y = 0; y < shape.length; y++){\n        for(let x = 0; x < shape[y].length; x++){\n            if (shape[y][x]) {\n                const newX = position.x + x;\n                const newY = position.y + y;\n                // Check boundaries\n                if (newX < 0 || newX >= _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH || newY >= _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT) {\n                    return false;\n                }\n                // Check collision with existing pieces (but allow negative Y for spawning)\n                if (newY >= 0 && board[newY][newX].filled) {\n                    return false;\n                }\n            }\n        }\n    }\n    return true;\n}\nfunction placePiece(board, piece) {\n    const newBoard = board.map((row)=>row.map((cell)=>({\n                ...cell\n            })));\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type][piece.rotation];\n    for(let y = 0; y < shape.length; y++){\n        for(let x = 0; x < shape[y].length; x++){\n            if (shape[y][x]) {\n                const boardX = piece.position.x + x;\n                const boardY = piece.position.y + y;\n                if (boardY >= 0 && boardY < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT && boardX >= 0 && boardX < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH) {\n                    newBoard[boardY][boardX] = {\n                        filled: true,\n                        type: piece.type\n                    };\n                }\n            }\n        }\n    }\n    return newBoard;\n}\nfunction clearLines(board) {\n    const newBoard = board.filter((row)=>!row.every((cell)=>cell.filled));\n    const linesCleared = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT - newBoard.length;\n    // Add empty rows at the top\n    while(newBoard.length < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT){\n        newBoard.unshift(Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH).fill(null).map(()=>({\n                filled: false\n            })));\n    }\n    return {\n        newBoard,\n        linesCleared\n    };\n}\nfunction rotatePiece(piece) {\n    const rotations = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type];\n    const newRotation = (piece.rotation + 1) % rotations.length;\n    return {\n        ...piece,\n        rotation: newRotation,\n        shape: rotations[newRotation]\n    };\n}\nfunction getGhostPiece(board, piece) {\n    let ghostPiece = {\n        ...piece\n    };\n    while(isValidPosition(board, ghostPiece, {\n        x: ghostPiece.position.x,\n        y: ghostPiece.position.y + 1\n    })){\n        ghostPiece = {\n            ...ghostPiece,\n            position: {\n                x: ghostPiece.position.x,\n                y: ghostPiece.position.y + 1\n            }\n        };\n    }\n    return ghostPiece;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./utils/tetris.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  var control;\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n\n  try {\n    // This should throw.\n    if (construct) {\n      // Something should be setting the props in the constructor.\n      var Fake = function () {\n        throw Error();\n      }; // $FlowFixMe[prop-missing]\n\n\n      Object.defineProperty(Fake.prototype, 'props', {\n        set: function () {\n          // We use a throwing setter instead of frozen or non-writable props\n          // because that won't throw in a non-strict mode function.\n          throw Error();\n        }\n      });\n\n      if (typeof Reflect === 'object' && Reflect.construct) {\n        // We construct a different control for this case to include any extra\n        // frames added by the construct call.\n        try {\n          Reflect.construct(Fake, []);\n        } catch (x) {\n          control = x;\n        }\n\n        Reflect.construct(fn, [], Fake);\n      } else {\n        try {\n          Fake.call();\n        } catch (x) {\n          control = x;\n        } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n        fn.call(Fake.prototype);\n      }\n    } else {\n      try {\n        throw Error();\n      } catch (x) {\n        control = x;\n      } // TODO(luna): This will currently only throw if the function component\n      // tries to access React/ReactDOM/props. We should probably make this throw\n      // in simple components too\n\n\n      var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n      // component, which we don't yet support. Attach a noop catch handler to\n      // silence the error.\n      // TODO: Implement component stacks for async client components?\n\n      if (maybePromise && typeof maybePromise.catch === 'function') {\n        maybePromise.catch(function () {});\n      }\n    }\n  } catch (sample) {\n    // This is inlined manually because closure doesn't do it for us.\n    if (sample && control && typeof sample.stack === 'string') {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sample.stack.split('\\n');\n      var controlLines = control.stack.split('\\n');\n      var s = sampleLines.length - 1;\n      var c = controlLines.length - 1;\n\n      while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n        // We expect at least one stack frame to be shared.\n        // Typically this will be the root most one. However, stack frames may be\n        // cut off due to maximum stack limits. In this case, one maybe cut off\n        // earlier than the other. We assume that the sample is longer or the same\n        // and there for cut off earlier. So we should find the root most frame in\n        // the sample somewhere in the control.\n        c--;\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ZjQyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);