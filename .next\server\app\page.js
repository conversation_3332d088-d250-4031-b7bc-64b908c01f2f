/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1Zpc2lvbm5haXJlLVJlcEdpdCU1Q1RldHJpcy1Qcm9qZWN0JTVDYXBwJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGV0cmlzLW5leHRqcy8/OWY2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFZpc2lvbm5haXJlLVJlcEdpdFxcXFxUZXRyaXMtUHJvamVjdFxcXFxhcHBcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_TetrisGame__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/TetrisGame */ \"(ssr)/./components/TetrisGame.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TetrisGame__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUQ7QUFFbEMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO2tCQUNDLDRFQUFDRiw4REFBVUE7Ozs7Ozs7Ozs7QUFHakIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90ZXRyaXMtbmV4dGpzLy4vYXBwL3BhZ2UudHN4Pzc2MDMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgVGV0cmlzR2FtZSBmcm9tICdAL2NvbXBvbmVudHMvVGV0cmlzR2FtZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPG1haW4+XG4gICAgICA8VGV0cmlzR2FtZSAvPlxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUZXRyaXNHYW1lIiwiSG9tZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Board.tsx":
/*!******************************!*\
  !*** ./components/Board.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Board)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(ssr)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Board({ board, currentPiece, ghostPiece }) {\n    // Create a display board that includes the current piece\n    const displayBoard = board.map((row)=>row.map((cell)=>({\n                ...cell\n            })));\n    // Add ghost piece to display board\n    if (ghostPiece) {\n        const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[ghostPiece.type][ghostPiece.rotation];\n        for(let y = 0; y < shape.length; y++){\n            for(let x = 0; x < shape[y].length; x++){\n                if (shape[y][x]) {\n                    const boardX = ghostPiece.position.x + x;\n                    const boardY = ghostPiece.position.y + y;\n                    if (boardY >= 0 && boardY < displayBoard.length && boardX >= 0 && boardX < displayBoard[0].length && !displayBoard[boardY][boardX].filled) {\n                        displayBoard[boardY][boardX] = {\n                            filled: true,\n                            type: ghostPiece.type,\n                            isGhost: true\n                        };\n                    }\n                }\n            }\n        }\n    }\n    // Add current piece to display board\n    if (currentPiece) {\n        const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[currentPiece.type][currentPiece.rotation];\n        for(let y = 0; y < shape.length; y++){\n            for(let x = 0; x < shape[y].length; x++){\n                if (shape[y][x]) {\n                    const boardX = currentPiece.position.x + x;\n                    const boardY = currentPiece.position.y + y;\n                    if (boardY >= 0 && boardY < displayBoard.length && boardX >= 0 && boardX < displayBoard[0].length) {\n                        displayBoard[boardY][boardX] = {\n                            filled: true,\n                            type: currentPiece.type\n                        };\n                    }\n                }\n            }\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"tetris-board\",\n        children: displayBoard.map((row, y)=>row.map((cell, x)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `tetris-cell ${cell.filled ? `tetris-cell-filled ${cell.isGhost ? `tetris-piece-${cell.type?.toLowerCase()} opacity-30` : `tetris-piece-${cell.type?.toLowerCase()}`}` : \"\"}`\n                }, `${x}-${y}`, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\Board.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)))\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\Board.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Board.tsx\n");

/***/ }),

/***/ "(ssr)/./components/GameControls.tsx":
/*!*************************************!*\
  !*** ./components/GameControls.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GameControls)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GameControls({ gameOver, paused, onStart, onPause, onResume, onRestart }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-3\",\n                children: \"Controls\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-4\",\n                children: gameOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onStart,\n                    className: \"game-button w-full\",\n                    children: \"New Game\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        paused ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onResume,\n                            className: \"game-button w-full\",\n                            children: \"Resume\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onPause,\n                            className: \"game-button w-full\",\n                            children: \"Pause\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onRestart,\n                            className: \"game-button w-full bg-red-600 hover:bg-red-700\",\n                            children: \"Restart\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm text-gray-300 space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold mb-2\",\n                        children: \"Keyboard Controls:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"← → : Move left/right\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"↓ : Soft drop\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"↑ : Rotate\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Space : Hard drop\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"P : Pause/Resume\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"R : Restart\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameControls.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/GameControls.tsx\n");

/***/ }),

/***/ "(ssr)/./components/GameStats.tsx":
/*!**********************************!*\
  !*** ./components/GameStats.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GameStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction GameStats({ stats }) {\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins.toString().padStart(2, \"0\")}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-3\",\n                children: \"Statistics\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Score:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-yellow-400\",\n                                children: stats.score.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Level:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-blue-400\",\n                                children: stats.level\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Lines:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-green-400\",\n                                children: stats.lines\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-300\",\n                                children: \"Time:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-mono text-purple-400\",\n                                children: formatTime(stats.time)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\GameStats.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0dhbWVTdGF0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBT1gsU0FBU0MsVUFBVSxFQUFFQyxLQUFLLEVBQWtCO0lBQ3pELE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEIsTUFBTUMsT0FBT0MsS0FBS0MsS0FBSyxDQUFDSCxVQUFVO1FBQ2xDLE1BQU1JLE9BQU9KLFVBQVU7UUFDdkIsT0FBTyxDQUFDLEVBQUVDLEtBQUtJLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQUVGLEtBQUtDLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDO0lBQ2xGO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBeUI7Ozs7OzswQkFFdkMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBZ0I7Ozs7OzswQ0FDaEMsOERBQUNFO2dDQUFLRixXQUFVOzBDQUE2QlYsTUFBTWEsS0FBSyxDQUFDQyxjQUFjOzs7Ozs7Ozs7Ozs7a0NBR3pFLDhEQUFDTDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFLRixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQTJCVixNQUFNZSxLQUFLOzs7Ozs7Ozs7Ozs7a0NBR3hELDhEQUFDTjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNFO2dDQUFLRixXQUFVOzBDQUFnQjs7Ozs7OzBDQUNoQyw4REFBQ0U7Z0NBQUtGLFdBQVU7MENBQTRCVixNQUFNZ0IsS0FBSzs7Ozs7Ozs7Ozs7O2tDQUd6RCw4REFBQ1A7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBS0YsV0FBVTswQ0FBZ0I7Ozs7OzswQ0FDaEMsOERBQUNFO2dDQUFLRixXQUFVOzBDQUE2QlQsV0FBV0QsTUFBTWlCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUs1RSIsInNvdXJjZXMiOlsid2VicGFjazovL3RldHJpcy1uZXh0anMvLi9jb21wb25lbnRzL0dhbWVTdGF0cy50c3g/MTMzNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBHYW1lU3RhdHMgYXMgR2FtZVN0YXRzVHlwZSB9IGZyb20gJ0AvdHlwZXMvdGV0cmlzJztcblxuaW50ZXJmYWNlIEdhbWVTdGF0c1Byb3BzIHtcbiAgc3RhdHM6IEdhbWVTdGF0c1R5cGU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdhbWVTdGF0cyh7IHN0YXRzIH06IEdhbWVTdGF0c1Byb3BzKSB7XG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAoc2Vjb25kczogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgICBjb25zdCBtaW5zID0gTWF0aC5mbG9vcihzZWNvbmRzIC8gNjApO1xuICAgIGNvbnN0IHNlY3MgPSBzZWNvbmRzICUgNjA7XG4gICAgcmV0dXJuIGAke21pbnMudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIHAtNCByb3VuZGVkLWxnIHNwYWNlLXktM1wiPlxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIG1iLTNcIj5TdGF0aXN0aWNzPC9oMz5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5TY29yZTo8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQteWVsbG93LTQwMFwiPntzdGF0cy5zY29yZS50b0xvY2FsZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPkxldmVsOjwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1ibHVlLTQwMFwiPntzdGF0cy5sZXZlbH08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDBcIj5MaW5lczo8L3NwYW4+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtZ3JlZW4tNDAwXCI+e3N0YXRzLmxpbmVzfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlRpbWU6PC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LXB1cnBsZS00MDBcIj57Zm9ybWF0VGltZShzdGF0cy50aW1lKX08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJHYW1lU3RhdHMiLCJzdGF0cyIsImZvcm1hdFRpbWUiLCJzZWNvbmRzIiwibWlucyIsIk1hdGgiLCJmbG9vciIsInNlY3MiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJzcGFuIiwic2NvcmUiLCJ0b0xvY2FsZVN0cmluZyIsImxldmVsIiwibGluZXMiLCJ0aW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/GameStats.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NextPiece.tsx":
/*!**********************************!*\
  !*** ./components/NextPiece.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextPiece)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(ssr)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NextPiece({ piece }) {\n    if (!piece) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-800 p-4 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-bold mb-2\",\n                    children: \"Next\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-16 h-16 bg-black border border-gray-600 rounded\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this);\n    }\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.PIECE_SHAPES[piece.type][0]; // Always show first rotation\n    const maxSize = Math.max(shape.length, shape[0]?.length || 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 p-4 rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-bold mb-2\",\n                children: \"Next\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-0 border border-gray-600 bg-black p-1 w-fit\",\n                style: {\n                    gridTemplateColumns: `repeat(${maxSize}, 1fr)`,\n                    gridTemplateRows: `repeat(${maxSize}, 1fr)`\n                },\n                children: Array(maxSize).fill(null).map((_, y)=>Array(maxSize).fill(null).map((_, x)=>{\n                        const isPartOfPiece = y < shape.length && x < shape[y].length && shape[y][x];\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-4 h-4 border border-gray-700 ${isPartOfPiece ? `tetris-piece-${piece.type.toLowerCase()}` : \"bg-black\"}`\n                        }, `${x}-${y}`, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 15\n                        }, this);\n                    }))\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\NextPiece.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NextPiece.tsx\n");

/***/ }),

/***/ "(ssr)/./components/TetrisGame.tsx":
/*!***********************************!*\
  !*** ./components/TetrisGame.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TetrisGame)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTetris */ \"(ssr)/./hooks/useTetris.ts\");\n/* harmony import */ var _hooks_useKeyboard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useKeyboard */ \"(ssr)/./hooks/useKeyboard.ts\");\n/* harmony import */ var _Board__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Board */ \"(ssr)/./components/Board.tsx\");\n/* harmony import */ var _NextPiece__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./NextPiece */ \"(ssr)/./components/NextPiece.tsx\");\n/* harmony import */ var _GameStats__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./GameStats */ \"(ssr)/./components/GameStats.tsx\");\n/* harmony import */ var _GameControls__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./GameControls */ \"(ssr)/./components/GameControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction TetrisGame() {\n    const { gameState, gameTime, ghostPiece, actions } = (0,_hooks_useTetris__WEBPACK_IMPORTED_MODULE_2__.useTetris)();\n    const keyboardActions = {\n        moveLeft: ()=>actions.movePiece(-1, 0),\n        moveRight: ()=>actions.movePiece(1, 0),\n        moveDown: ()=>actions.movePiece(0, 1),\n        rotate: actions.rotatePiece,\n        hardDrop: actions.hardDrop,\n        pause: actions.pauseGame,\n        resume: actions.resumeGame,\n        restart: actions.startNewGame\n    };\n    (0,_hooks_useKeyboard__WEBPACK_IMPORTED_MODULE_3__.useKeyboard)({\n        actions: keyboardActions,\n        gameOver: gameState.gameOver,\n        paused: gameState.paused\n    });\n    const gameStats = {\n        score: gameState.score,\n        level: gameState.level,\n        lines: gameState.lines,\n        time: gameTime\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center mb-8 text-white\",\n                    children: \"Tetris\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NextPiece__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    piece: gameState.nextPiece\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GameStats__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    stats: gameStats\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Board__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            board: gameState.board,\n                                            currentPiece: gameState.currentPiece,\n                                            ghostPiece: ghostPiece\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        !gameState.currentPiece && !gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Tetris\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-4\",\n                                                        children: \"Ready to play?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.startNewGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Start Game\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this),\n                                        gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Game Over\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl mb-4\",\n                                                        children: [\n                                                            \"Score: \",\n                                                            gameState.score.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.startNewGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Play Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        gameState.paused && !gameState.gameOver && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold mb-4\",\n                                                        children: \"Paused\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: actions.resumeGame,\n                                                        className: \"game-button text-lg px-6 py-3\",\n                                                        children: \"Resume\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden grid grid-cols-3 gap-2 w-full max-w-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(-1, 0),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"←\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: actions.rotatePiece,\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"↻\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(1, 0),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>actions.movePiece(0, 1),\n                                            className: \"game-button py-3\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: actions.hardDrop,\n                                            className: \"game-button py-3 bg-red-600 hover:bg-red-700\",\n                                            disabled: gameState.gameOver || gameState.paused,\n                                            children: \"Drop\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: gameState.paused ? actions.resumeGame : actions.pauseGame,\n                                            className: \"game-button py-3 bg-yellow-600 hover:bg-yellow-700\",\n                                            disabled: gameState.gameOver,\n                                            children: gameState.paused ? \"▶\" : \"⏸\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GameControls__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                gameOver: gameState.gameOver,\n                                paused: gameState.paused,\n                                onStart: actions.startNewGame,\n                                onPause: actions.pauseGame,\n                                onResume: actions.resumeGame,\n                                onRestart: actions.startNewGame\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center text-gray-300 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Use arrow keys to move and rotate pieces. Space for hard drop, P to pause, R to restart.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\components\\\\TetrisGame.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/TetrisGame.tsx\n");

/***/ }),

/***/ "(ssr)/./constants/tetris.ts":
/*!*****************************!*\
  !*** ./constants/tetris.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BOARD_HEIGHT: () => (/* binding */ BOARD_HEIGHT),\n/* harmony export */   BOARD_WIDTH: () => (/* binding */ BOARD_WIDTH),\n/* harmony export */   INITIAL_DROP_TIME: () => (/* binding */ INITIAL_DROP_TIME),\n/* harmony export */   LEVEL_SPEED_INCREASE: () => (/* binding */ LEVEL_SPEED_INCREASE),\n/* harmony export */   LINES_PER_LEVEL: () => (/* binding */ LINES_PER_LEVEL),\n/* harmony export */   PIECE_SHAPES: () => (/* binding */ PIECE_SHAPES),\n/* harmony export */   PIECE_TYPES: () => (/* binding */ PIECE_TYPES),\n/* harmony export */   POINTS: () => (/* binding */ POINTS)\n/* harmony export */ });\nconst BOARD_WIDTH = 10;\nconst BOARD_HEIGHT = 20;\nconst PIECE_SHAPES = {\n    I: [\n        [\n            [\n                0,\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0,\n                0\n            ],\n            [\n                0,\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    O: [\n        [\n            [\n                1,\n                1\n            ],\n            [\n                1,\n                1\n            ]\n        ]\n    ],\n    T: [\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    S: [\n        [\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                1\n            ]\n        ]\n    ],\n    Z: [\n        [\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                1\n            ],\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ],\n    J: [\n        [\n            [\n                1,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                1\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                1\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                1,\n                1,\n                0\n            ]\n        ]\n    ],\n    L: [\n        [\n            [\n                0,\n                0,\n                1\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                0,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                1\n            ]\n        ],\n        [\n            [\n                0,\n                0,\n                0\n            ],\n            [\n                1,\n                1,\n                1\n            ],\n            [\n                1,\n                0,\n                0\n            ]\n        ],\n        [\n            [\n                1,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ],\n            [\n                0,\n                1,\n                0\n            ]\n        ]\n    ]\n};\nconst PIECE_TYPES = [\n    \"I\",\n    \"O\",\n    \"T\",\n    \"S\",\n    \"Z\",\n    \"J\",\n    \"L\"\n];\nconst INITIAL_DROP_TIME = 1000; // milliseconds\nconst LEVEL_SPEED_INCREASE = 0.9; // multiply drop time by this each level\nconst POINTS = {\n    SINGLE: 100,\n    DOUBLE: 300,\n    TRIPLE: 500,\n    TETRIS: 800,\n    SOFT_DROP: 1,\n    HARD_DROP: 2\n};\nconst LINES_PER_LEVEL = 10;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./constants/tetris.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useKeyboard.ts":
/*!******************************!*\
  !*** ./hooks/useKeyboard.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useKeyboard: () => (/* binding */ useKeyboard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useKeyboard auto */ \nfunction useKeyboard({ actions, gameOver, paused }) {\n    const keysPressed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Set());\n    const lastMoveTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const REPEAT_DELAY = 150; // milliseconds\n    const INITIAL_DELAY = 200; // milliseconds\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            const key = event.key.toLowerCase();\n            const now = Date.now();\n            // Prevent default for game keys\n            if ([\n                \"arrowleft\",\n                \"arrowright\",\n                \"arrowdown\",\n                \"arrowup\",\n                \" \",\n                \"p\",\n                \"r\"\n            ].includes(key)) {\n                event.preventDefault();\n            }\n            // Handle single-press keys\n            if (!keysPressed.current.has(key)) {\n                keysPressed.current.add(key);\n                lastMoveTime.current[key] = now;\n                switch(key){\n                    case \"arrowup\":\n                        if (!gameOver && !paused) {\n                            actions.rotate();\n                        }\n                        break;\n                    case \" \":\n                        if (!gameOver && !paused) {\n                            actions.hardDrop();\n                        }\n                        break;\n                    case \"p\":\n                        if (!gameOver) {\n                            if (paused) {\n                                actions.resume();\n                            } else {\n                                actions.pause();\n                            }\n                        }\n                        break;\n                    case \"r\":\n                        actions.restart();\n                        break;\n                }\n            }\n            // Handle repeating keys\n            if (keysPressed.current.has(key)) {\n                const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);\n                const delay = lastMoveTime.current[key] === now ? INITIAL_DELAY : REPEAT_DELAY;\n                if (timeSinceLastMove >= delay) {\n                    lastMoveTime.current[key] = now;\n                    switch(key){\n                        case \"arrowleft\":\n                            if (!gameOver && !paused) {\n                                actions.moveLeft();\n                            }\n                            break;\n                        case \"arrowright\":\n                            if (!gameOver && !paused) {\n                                actions.moveRight();\n                            }\n                            break;\n                        case \"arrowdown\":\n                            if (!gameOver && !paused) {\n                                actions.moveDown();\n                            }\n                            break;\n                    }\n                }\n            }\n        };\n        const handleKeyUp = (event)=>{\n            const key = event.key.toLowerCase();\n            keysPressed.current.delete(key);\n            delete lastMoveTime.current[key];\n        };\n        // Handle repeating movement\n        const handleRepeat = ()=>{\n            const now = Date.now();\n            keysPressed.current.forEach((key)=>{\n                const timeSinceLastMove = now - (lastMoveTime.current[key] || 0);\n                if (timeSinceLastMove >= REPEAT_DELAY) {\n                    lastMoveTime.current[key] = now;\n                    switch(key){\n                        case \"arrowleft\":\n                            if (!gameOver && !paused) {\n                                actions.moveLeft();\n                            }\n                            break;\n                        case \"arrowright\":\n                            if (!gameOver && !paused) {\n                                actions.moveRight();\n                            }\n                            break;\n                        case \"arrowdown\":\n                            if (!gameOver && !paused) {\n                                actions.moveDown();\n                            }\n                            break;\n                    }\n                }\n            });\n        };\n        window.addEventListener(\"keydown\", handleKeyDown);\n        window.addEventListener(\"keyup\", handleKeyUp);\n        const intervalId = setInterval(handleRepeat, 50);\n        return ()=>{\n            window.removeEventListener(\"keydown\", handleKeyDown);\n            window.removeEventListener(\"keyup\", handleKeyUp);\n            clearInterval(intervalId);\n        };\n    }, [\n        actions,\n        gameOver,\n        paused\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useKeyboard.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useTetris.ts":
/*!****************************!*\
  !*** ./hooks/useTetris.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTetris: () => (/* binding */ useTetris)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_tetris__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/tetris */ \"(ssr)/./utils/tetris.ts\");\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/tetris */ \"(ssr)/./constants/tetris.ts\");\n/* __next_internal_client_entry_do_not_use__ useTetris auto */ \n\n\nfunction useTetris() {\n    const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        board: (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createEmptyBoard)(),\n        currentPiece: null,\n        nextPiece: null,\n        score: 0,\n        level: 1,\n        lines: 0,\n        gameOver: false,\n        paused: false\n    });\n    const [gameTime, setGameTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const dropTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(_constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME);\n    const lastDropRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const gameLoopRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const timeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    // Initialize game on first load\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!gameState.currentPiece && !gameState.gameOver) {\n            const firstPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            const secondPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            setGameState((prev)=>({\n                    ...prev,\n                    currentPiece: firstPiece,\n                    nextPiece: secondPiece\n                }));\n        }\n    }, []);\n    const calculateDropTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((level)=>{\n        return Math.max(50, _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME * Math.pow(_constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LEVEL_SPEED_INCREASE, level - 1));\n    }, []);\n    const startNewGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const firstPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n        const secondPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n        setGameState({\n            board: (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createEmptyBoard)(),\n            currentPiece: firstPiece,\n            nextPiece: secondPiece,\n            score: 0,\n            level: 1,\n            lines: 0,\n            gameOver: false,\n            paused: false\n        });\n        setGameTime(0);\n        dropTimeRef.current = _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.INITIAL_DROP_TIME;\n        lastDropRef.current = Date.now();\n    }, []);\n    const pauseGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>({\n                ...prev,\n                paused: true\n            }));\n    }, []);\n    const resumeGame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>({\n                ...prev,\n                paused: false\n            }));\n        lastDropRef.current = Date.now();\n    }, []);\n    const movePiece = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dx, dy)=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const newPosition = {\n                x: prev.currentPiece.position.x + dx,\n                y: prev.currentPiece.position.y + dy\n            };\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, newPosition)) {\n                return {\n                    ...prev,\n                    currentPiece: {\n                        ...prev.currentPiece,\n                        position: newPosition\n                    }\n                };\n            }\n            return prev;\n        });\n    }, []);\n    const rotatePieceAction = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const rotatedPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.rotatePiece)(prev.currentPiece);\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, rotatedPiece, rotatedPiece.position)) {\n                return {\n                    ...prev,\n                    currentPiece: rotatedPiece\n                };\n            }\n            return prev;\n        });\n    }, []);\n    const hardDrop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            let dropDistance = 0;\n            let newY = prev.currentPiece.position.y;\n            while((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, {\n                x: prev.currentPiece.position.x,\n                y: newY + 1\n            })){\n                newY++;\n                dropDistance++;\n            }\n            const droppedPiece = {\n                ...prev.currentPiece,\n                position: {\n                    x: prev.currentPiece.position.x,\n                    y: newY\n                }\n            };\n            const newBoard = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.placePiece)(prev.board, droppedPiece);\n            const { newBoard: clearedBoard, linesCleared } = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.clearLines)(newBoard);\n            const newLines = prev.lines + linesCleared;\n            const newLevel = Math.floor(newLines / _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LINES_PER_LEVEL) + 1;\n            let scoreIncrease = dropDistance * _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.HARD_DROP;\n            if (linesCleared > 0) {\n                const linePoints = [\n                    0,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.SINGLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.DOUBLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TRIPLE,\n                    _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TETRIS\n                ];\n                scoreIncrease += linePoints[linesCleared] * newLevel;\n            }\n            const newPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n            const gameOver = !(0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(clearedBoard, prev.nextPiece, prev.nextPiece.position);\n            return {\n                ...prev,\n                board: clearedBoard,\n                currentPiece: gameOver ? null : prev.nextPiece,\n                nextPiece: gameOver ? null : newPiece,\n                score: prev.score + scoreIncrease,\n                level: newLevel,\n                lines: newLines,\n                gameOver\n            };\n        });\n    }, []);\n    const dropPiece = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setGameState((prev)=>{\n            if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;\n            const newPosition = {\n                x: prev.currentPiece.position.x,\n                y: prev.currentPiece.position.y + 1\n            };\n            if ((0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(prev.board, prev.currentPiece, newPosition)) {\n                return {\n                    ...prev,\n                    currentPiece: {\n                        ...prev.currentPiece,\n                        position: newPosition\n                    }\n                };\n            } else {\n                // Piece has landed\n                const newBoard = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.placePiece)(prev.board, prev.currentPiece);\n                const { newBoard: clearedBoard, linesCleared } = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.clearLines)(newBoard);\n                const newLines = prev.lines + linesCleared;\n                const newLevel = Math.floor(newLines / _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.LINES_PER_LEVEL) + 1;\n                let scoreIncrease = 0;\n                if (linesCleared > 0) {\n                    const linePoints = [\n                        0,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.SINGLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.DOUBLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TRIPLE,\n                        _constants_tetris__WEBPACK_IMPORTED_MODULE_2__.POINTS.TETRIS\n                    ];\n                    scoreIncrease = linePoints[linesCleared] * newLevel;\n                }\n                const newPiece = (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.createRandomPiece)();\n                const gameOver = prev.nextPiece ? !(0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.isValidPosition)(clearedBoard, prev.nextPiece, prev.nextPiece.position) : true;\n                return {\n                    ...prev,\n                    board: clearedBoard,\n                    currentPiece: gameOver ? null : prev.nextPiece,\n                    nextPiece: gameOver ? null : newPiece,\n                    score: prev.score + scoreIncrease,\n                    level: newLevel,\n                    lines: newLines,\n                    gameOver\n                };\n            }\n        });\n    }, []);\n    // Game loop\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (gameState.gameOver || gameState.paused || !gameState.currentPiece) {\n            if (gameLoopRef.current) {\n                cancelAnimationFrame(gameLoopRef.current);\n            }\n            return;\n        }\n        const gameLoop = ()=>{\n            const now = Date.now();\n            const dropTime = calculateDropTime(gameState.level);\n            if (now - lastDropRef.current > dropTime) {\n                dropPiece();\n                lastDropRef.current = now;\n            }\n            gameLoopRef.current = requestAnimationFrame(gameLoop);\n        };\n        gameLoopRef.current = requestAnimationFrame(gameLoop);\n        return ()=>{\n            if (gameLoopRef.current) {\n                cancelAnimationFrame(gameLoopRef.current);\n            }\n        };\n    }, [\n        gameState.gameOver,\n        gameState.paused,\n        gameState.currentPiece,\n        gameState.level,\n        dropPiece,\n        calculateDropTime\n    ]);\n    // Timer\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (gameState.gameOver || gameState.paused) {\n            if (timeRef.current) {\n                clearInterval(timeRef.current);\n            }\n            return;\n        }\n        timeRef.current = window.setInterval(()=>{\n            setGameTime((prev)=>prev + 1);\n        }, 1000);\n        return ()=>{\n            if (timeRef.current) {\n                clearInterval(timeRef.current);\n            }\n        };\n    }, [\n        gameState.gameOver,\n        gameState.paused\n    ]);\n    const ghostPiece = gameState.currentPiece ? (0,_utils_tetris__WEBPACK_IMPORTED_MODULE_1__.getGhostPiece)(gameState.board, gameState.currentPiece) : null;\n    return {\n        gameState,\n        gameTime,\n        ghostPiece,\n        actions: {\n            startNewGame,\n            pauseGame,\n            resumeGame,\n            movePiece,\n            rotatePiece: rotatePieceAction,\n            hardDrop,\n            dropPiece\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useTetris.ts\n");

/***/ }),

/***/ "(ssr)/./utils/tetris.ts":
/*!*************************!*\
  !*** ./utils/tetris.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearLines: () => (/* binding */ clearLines),\n/* harmony export */   createEmptyBoard: () => (/* binding */ createEmptyBoard),\n/* harmony export */   createRandomPiece: () => (/* binding */ createRandomPiece),\n/* harmony export */   getGhostPiece: () => (/* binding */ getGhostPiece),\n/* harmony export */   isValidPosition: () => (/* binding */ isValidPosition),\n/* harmony export */   placePiece: () => (/* binding */ placePiece),\n/* harmony export */   rotatePiece: () => (/* binding */ rotatePiece)\n/* harmony export */ });\n/* harmony import */ var _constants_tetris__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/constants/tetris */ \"(ssr)/./constants/tetris.ts\");\n\nfunction createEmptyBoard() {\n    return Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT).fill(null).map(()=>Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH).fill(null).map(()=>({\n                filled: false\n            })));\n}\nfunction createRandomPiece() {\n    const type = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_TYPES[Math.floor(Math.random() * _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_TYPES.length)];\n    return {\n        type,\n        shape: _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[type][0],\n        position: {\n            x: Math.floor(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH / 2) - 1,\n            y: 0\n        },\n        rotation: 0\n    };\n}\nfunction isValidPosition(board, piece, position) {\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type][piece.rotation];\n    for(let y = 0; y < shape.length; y++){\n        for(let x = 0; x < shape[y].length; x++){\n            if (shape[y][x]) {\n                const newX = position.x + x;\n                const newY = position.y + y;\n                // Check boundaries\n                if (newX < 0 || newX >= _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH || newY >= _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT) {\n                    return false;\n                }\n                // Check collision with existing pieces (but allow negative Y for spawning)\n                if (newY >= 0 && board[newY][newX].filled) {\n                    return false;\n                }\n            }\n        }\n    }\n    return true;\n}\nfunction placePiece(board, piece) {\n    const newBoard = board.map((row)=>row.map((cell)=>({\n                ...cell\n            })));\n    const shape = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type][piece.rotation];\n    for(let y = 0; y < shape.length; y++){\n        for(let x = 0; x < shape[y].length; x++){\n            if (shape[y][x]) {\n                const boardX = piece.position.x + x;\n                const boardY = piece.position.y + y;\n                if (boardY >= 0 && boardY < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT && boardX >= 0 && boardX < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH) {\n                    newBoard[boardY][boardX] = {\n                        filled: true,\n                        type: piece.type\n                    };\n                }\n            }\n        }\n    }\n    return newBoard;\n}\nfunction clearLines(board) {\n    const newBoard = board.filter((row)=>!row.every((cell)=>cell.filled));\n    const linesCleared = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT - newBoard.length;\n    // Add empty rows at the top\n    while(newBoard.length < _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_HEIGHT){\n        newBoard.unshift(Array(_constants_tetris__WEBPACK_IMPORTED_MODULE_0__.BOARD_WIDTH).fill(null).map(()=>({\n                filled: false\n            })));\n    }\n    return {\n        newBoard,\n        linesCleared\n    };\n}\nfunction rotatePiece(piece) {\n    const rotations = _constants_tetris__WEBPACK_IMPORTED_MODULE_0__.PIECE_SHAPES[piece.type];\n    const newRotation = (piece.rotation + 1) % rotations.length;\n    return {\n        ...piece,\n        rotation: newRotation,\n        shape: rotations[newRotation]\n    };\n}\nfunction getGhostPiece(board, piece) {\n    let ghostPiece = {\n        ...piece\n    };\n    while(isValidPosition(board, ghostPiece, {\n        x: ghostPiece.position.x,\n        y: ghostPiece.position.y + 1\n    })){\n        ghostPiece = {\n            ...ghostPiece,\n            position: {\n                x: ghostPiece.position.x,\n                y: ghostPiece.position.y + 1\n            }\n        };\n    }\n    return ghostPiece;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/tetris.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"51833d8449c6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90ZXRyaXMtbmV4dGpzLy4vYXBwL2dsb2JhbHMuY3NzP2Y4NGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1MTgzM2Q4NDQ5YzZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Tetris Game - Next.js\",\n    description: \"A classic Tetris game built with Next.js and TypeScript\",\n    keywords: [\n        \"tetris\",\n        \"game\",\n        \"nextjs\",\n        \"typescript\",\n        \"puzzle\"\n    ],\n    authors: [\n        {\n            name: \"Tetris Game Developer\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#1f2937\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Visionnaire-RepGit\\\\Tetris-Project\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7UUFBQztRQUFVO1FBQVE7UUFBVTtRQUFjO0tBQVM7SUFDOURDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQXdCO0tBQUU7SUFDNUNDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7OzBCQUNULDhEQUFDQzs7a0NBQ0MsOERBQUNDO3dCQUFLUCxNQUFLO3dCQUFjUSxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzs7Ozs7Ozs7Ozs7OzBCQUV4Qiw4REFBQ0M7Z0JBQUtDLFdBQVU7MEJBQ2JWOzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL3RldHJpcy1uZXh0anMvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1RldHJpcyBHYW1lIC0gTmV4dC5qcycsXG4gIGRlc2NyaXB0aW9uOiAnQSBjbGFzc2ljIFRldHJpcyBnYW1lIGJ1aWx0IHdpdGggTmV4dC5qcyBhbmQgVHlwZVNjcmlwdCcsXG4gIGtleXdvcmRzOiBbJ3RldHJpcycsICdnYW1lJywgJ25leHRqcycsICd0eXBlc2NyaXB0JywgJ3B1enpsZSddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnVGV0cmlzIEdhbWUgRGV2ZWxvcGVyJyB9XSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjMWYyOTM3XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiYW50aWFsaWFzZWRcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiaGVhZCIsIm1ldGEiLCJjb250ZW50IiwibGluayIsInJlbCIsImhyZWYiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Visionnaire-RepGit\Tetris-Project\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVisionnaire-RepGit%5CTetris-Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();