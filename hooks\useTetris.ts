'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { GameState, Piece } from '@/types/tetris';
import { 
  createEmptyBoard, 
  createRandomPiece, 
  isValidPosition, 
  placePiece, 
  clearLines, 
  rotatePiece,
  getGhostPiece
} from '@/utils/tetris';
import { 
  INITIAL_DROP_TIME, 
  LEVEL_SPEED_INCREASE, 
  POINTS, 
  LINES_PER_LEVEL 
} from '@/constants/tetris';

export function useTetris() {
  const [gameState, setGameState] = useState<GameState>({
    board: createEmptyBoard(),
    currentPiece: null,
    nextPiece: null,
    score: 0,
    level: 1,
    lines: 0,
    gameOver: false,
    paused: false
  });

  const [gameTime, setGameTime] = useState(0);
  const dropTimeRef = useRef(INITIAL_DROP_TIME);
  const lastDropRef = useRef(0);
  const gameLoopRef = useRef<number>();
  const timeRef = useRef<number>();

  // Initialize game on first load
  useEffect(() => {
    if (!gameState.currentPiece && !gameState.gameOver) {
      const firstPiece = createRandomPiece();
      const secondPiece = createRandomPiece();

      setGameState(prev => ({
        ...prev,
        currentPiece: firstPiece,
        nextPiece: secondPiece
      }));
    }
  }, []);

  const calculateDropTime = useCallback((level: number) => {
    return Math.max(50, INITIAL_DROP_TIME * Math.pow(LEVEL_SPEED_INCREASE, level - 1));
  }, []);

  const startNewGame = useCallback(() => {
    const firstPiece = createRandomPiece();
    const secondPiece = createRandomPiece();
    
    setGameState({
      board: createEmptyBoard(),
      currentPiece: firstPiece,
      nextPiece: secondPiece,
      score: 0,
      level: 1,
      lines: 0,
      gameOver: false,
      paused: false
    });
    
    setGameTime(0);
    dropTimeRef.current = INITIAL_DROP_TIME;
    lastDropRef.current = Date.now();
  }, []);

  const pauseGame = useCallback(() => {
    setGameState(prev => ({ ...prev, paused: true }));
  }, []);

  const resumeGame = useCallback(() => {
    setGameState(prev => ({ ...prev, paused: false }));
    lastDropRef.current = Date.now();
  }, []);

  const movePiece = useCallback((dx: number, dy: number) => {
    setGameState(prev => {
      if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;
      
      const newPosition = {
        x: prev.currentPiece.position.x + dx,
        y: prev.currentPiece.position.y + dy
      };
      
      if (isValidPosition(prev.board, prev.currentPiece, newPosition)) {
        return {
          ...prev,
          currentPiece: {
            ...prev.currentPiece,
            position: newPosition
          }
        };
      }
      
      return prev;
    });
  }, []);

  const rotatePieceAction = useCallback(() => {
    setGameState(prev => {
      if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;
      
      const rotatedPiece = rotatePiece(prev.currentPiece);
      
      if (isValidPosition(prev.board, rotatedPiece, rotatedPiece.position)) {
        return {
          ...prev,
          currentPiece: rotatedPiece
        };
      }
      
      return prev;
    });
  }, []);

  const hardDrop = useCallback(() => {
    setGameState(prev => {
      if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;
      
      let dropDistance = 0;
      let newY = prev.currentPiece.position.y;
      
      while (isValidPosition(prev.board, prev.currentPiece, { 
        x: prev.currentPiece.position.x, 
        y: newY + 1 
      })) {
        newY++;
        dropDistance++;
      }
      
      const droppedPiece = {
        ...prev.currentPiece,
        position: { x: prev.currentPiece.position.x, y: newY }
      };
      
      const newBoard = placePiece(prev.board, droppedPiece);
      const { newBoard: clearedBoard, linesCleared } = clearLines(newBoard);
      
      const newLines = prev.lines + linesCleared;
      const newLevel = Math.floor(newLines / LINES_PER_LEVEL) + 1;
      
      let scoreIncrease = dropDistance * POINTS.HARD_DROP;
      if (linesCleared > 0) {
        const linePoints = [0, POINTS.SINGLE, POINTS.DOUBLE, POINTS.TRIPLE, POINTS.TETRIS];
        scoreIncrease += linePoints[linesCleared] * newLevel;
      }
      
      const newPiece = createRandomPiece();
      const gameOver = !isValidPosition(clearedBoard, prev.nextPiece!, prev.nextPiece!.position);
      
      return {
        ...prev,
        board: clearedBoard,
        currentPiece: gameOver ? null : prev.nextPiece,
        nextPiece: gameOver ? null : newPiece,
        score: prev.score + scoreIncrease,
        level: newLevel,
        lines: newLines,
        gameOver
      };
    });
  }, []);

  const dropPiece = useCallback(() => {
    setGameState(prev => {
      if (!prev.currentPiece || prev.gameOver || prev.paused) return prev;

      const newPosition = {
        x: prev.currentPiece.position.x,
        y: prev.currentPiece.position.y + 1
      };

      if (isValidPosition(prev.board, prev.currentPiece, newPosition)) {
        return {
          ...prev,
          currentPiece: {
            ...prev.currentPiece,
            position: newPosition
          }
        };
      } else {
        // Piece has landed
        const newBoard = placePiece(prev.board, prev.currentPiece);
        const { newBoard: clearedBoard, linesCleared } = clearLines(newBoard);

        const newLines = prev.lines + linesCleared;
        const newLevel = Math.floor(newLines / LINES_PER_LEVEL) + 1;

        let scoreIncrease = 0;
        if (linesCleared > 0) {
          const linePoints = [0, POINTS.SINGLE, POINTS.DOUBLE, POINTS.TRIPLE, POINTS.TETRIS];
          scoreIncrease = linePoints[linesCleared] * newLevel;
        }

        const newPiece = createRandomPiece();
        const gameOver = prev.nextPiece ? !isValidPosition(clearedBoard, prev.nextPiece, prev.nextPiece.position) : true;

        return {
          ...prev,
          board: clearedBoard,
          currentPiece: gameOver ? null : prev.nextPiece,
          nextPiece: gameOver ? null : newPiece,
          score: prev.score + scoreIncrease,
          level: newLevel,
          lines: newLines,
          gameOver
        };
      }
    });
  }, []);

  // Game loop
  useEffect(() => {
    if (gameState.gameOver || gameState.paused || !gameState.currentPiece) {
      if (gameLoopRef.current) {
        cancelAnimationFrame(gameLoopRef.current);
      }
      return;
    }

    const gameLoop = () => {
      const now = Date.now();
      const dropTime = calculateDropTime(gameState.level);
      
      if (now - lastDropRef.current > dropTime) {
        dropPiece();
        lastDropRef.current = now;
      }
      
      gameLoopRef.current = requestAnimationFrame(gameLoop);
    };

    gameLoopRef.current = requestAnimationFrame(gameLoop);

    return () => {
      if (gameLoopRef.current) {
        cancelAnimationFrame(gameLoopRef.current);
      }
    };
  }, [gameState.gameOver, gameState.paused, gameState.currentPiece, gameState.level, dropPiece, calculateDropTime]);

  // Timer
  useEffect(() => {
    if (gameState.gameOver || gameState.paused) {
      if (timeRef.current) {
        clearInterval(timeRef.current);
      }
      return;
    }

    timeRef.current = window.setInterval(() => {
      setGameTime(prev => prev + 1);
    }, 1000);

    return () => {
      if (timeRef.current) {
        clearInterval(timeRef.current);
      }
    };
  }, [gameState.gameOver, gameState.paused]);

  const ghostPiece = gameState.currentPiece ? getGhostPiece(gameState.board, gameState.currentPiece) : null;

  return {
    gameState,
    gameTime,
    ghostPiece,
    actions: {
      startNewGame,
      pauseGame,
      resumeGame,
      movePiece,
      rotatePiece: rotatePieceAction,
      hardDrop,
      dropPiece
    }
  };
}
