/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        tetris: {
          cyan: '#00FFFF',    // I-piece - <PERSON>an brilhante
          blue: '#0066FF',    // J-piece - Azul vibrante
          orange: '#FF6600',  // L-piece - Laranja vibrante
          yellow: '#FFFF00',  // O-piece - Amarelo brilhante
          green: '#00FF66',   // S-piece - Verde vibrante
          purple: '#9900FF',  // T-piece - Roxo vibrante
          red: '#FF3333',     // Z-piece - Vermelho vibrante
          gray: '#c0c0c0',
          dark: '#404040',
        }
      },
      animation: {
        'fall': 'fall 1s linear infinite',
        'clear-line': 'clearLine 0.5s ease-in-out',
      },
      keyframes: {
        fall: {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(100%)' }
        },
        clearLine: {
          '0%': { opacity: '1', backgroundColor: '#ffffff' },
          '50%': { opacity: '0.5', backgroundColor: '#ffff00' },
          '100%': { opacity: '0' }
        }
      }
    },
  },
  plugins: [],
}
