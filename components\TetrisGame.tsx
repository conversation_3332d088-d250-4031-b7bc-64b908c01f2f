'use client';

import React from 'react';
import { useTetris } from '@/hooks/useTetris';
import { useKeyboard } from '@/hooks/useKeyboard';
import Board from './Board';
import NextPiece from './NextPiece';
import GameStats from './GameStats';
import GameControls from './GameControls';

export default function TetrisGame() {
  const { gameState, gameTime, ghostPiece, actions } = useTetris();

  const keyboardActions = {
    moveLeft: () => actions.movePiece(-1, 0),
    moveRight: () => actions.movePiece(1, 0),
    moveDown: () => actions.movePiece(0, 1),
    rotate: actions.rotatePiece,
    hardDrop: actions.hardDrop,
    pause: actions.pauseGame,
    resume: actions.resumeGame,
    restart: actions.startNewGame,
  };

  useKeyboard({
    actions: keyboardActions,
    gameOver: gameState.gameOver,
    paused: gameState.paused,
  });

  const gameStats = {
    score: gameState.score,
    level: gameState.level,
    lines: gameState.lines,
    time: gameTime,
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-white">
          Tetris
        </h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Panel */}
          <div className="space-y-4">
            <NextPiece piece={gameState.nextPiece} />
            <GameStats stats={gameStats} />
          </div>
          
          {/* Game Board */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Board
                board={gameState.board}
                currentPiece={gameState.currentPiece}
                ghostPiece={ghostPiece}
              />

              {/* Start Game Overlay */}
              {!gameState.currentPiece && !gameState.gameOver && (
                <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Tetris</h2>
                    <p className="text-lg mb-4">Ready to play?</p>
                    <button
                      onClick={actions.startNewGame}
                      className="game-button text-lg px-6 py-3"
                    >
                      Start Game
                    </button>
                  </div>
                </div>
              )}
              
              {/* Game Over Overlay */}
              {gameState.gameOver && (
                <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Game Over</h2>
                    <p className="text-xl mb-4">Score: {gameState.score.toLocaleString()}</p>
                    <button 
                      onClick={actions.startNewGame}
                      className="game-button text-lg px-6 py-3"
                    >
                      Play Again
                    </button>
                  </div>
                </div>
              )}
              
              {/* Pause Overlay */}
              {gameState.paused && !gameState.gameOver && (
                <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h2 className="text-3xl font-bold mb-4">Paused</h2>
                    <button 
                      onClick={actions.resumeGame}
                      className="game-button text-lg px-6 py-3"
                    >
                      Resume
                    </button>
                  </div>
                </div>
              )}
            </div>
            
            {/* Mobile Controls */}
            <div className="lg:hidden grid grid-cols-3 gap-2 w-full max-w-xs">
              <button 
                onClick={() => actions.movePiece(-1, 0)}
                className="game-button py-3"
                disabled={gameState.gameOver || gameState.paused}
              >
                ←
              </button>
              <button 
                onClick={actions.rotatePiece}
                className="game-button py-3"
                disabled={gameState.gameOver || gameState.paused}
              >
                ↻
              </button>
              <button 
                onClick={() => actions.movePiece(1, 0)}
                className="game-button py-3"
                disabled={gameState.gameOver || gameState.paused}
              >
                →
              </button>
              <button 
                onClick={() => actions.movePiece(0, 1)}
                className="game-button py-3"
                disabled={gameState.gameOver || gameState.paused}
              >
                ↓
              </button>
              <button 
                onClick={actions.hardDrop}
                className="game-button py-3 bg-red-600 hover:bg-red-700"
                disabled={gameState.gameOver || gameState.paused}
              >
                Drop
              </button>
              <button 
                onClick={gameState.paused ? actions.resumeGame : actions.pauseGame}
                className="game-button py-3 bg-yellow-600 hover:bg-yellow-700"
                disabled={gameState.gameOver}
              >
                {gameState.paused ? '▶' : '⏸'}
              </button>
            </div>
          </div>
          
          {/* Right Panel */}
          <div>
            <GameControls
              gameOver={gameState.gameOver}
              paused={gameState.paused}
              onStart={actions.startNewGame}
              onPause={actions.pauseGame}
              onResume={actions.resumeGame}
              onRestart={actions.startNewGame}
            />
          </div>
        </div>
        
        {/* Instructions */}
        <div className="mt-8 text-center text-gray-300 text-sm">
          <p>Use arrow keys to move and rotate pieces. Space for hard drop, P to pause, R to restart.</p>
        </div>
      </div>
    </div>
  );
}
