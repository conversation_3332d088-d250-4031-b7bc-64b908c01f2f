export type PieceType = 'I' | 'O' | 'T' | 'S' | 'Z' | 'J' | 'L';

export interface Position {
  x: number;
  y: number;
}

export interface Piece {
  type: PieceType;
  shape: number[][];
  position: Position;
  rotation: number;
}

export interface Cell {
  filled: boolean;
  type?: PieceType;
  isGhost?: boolean;
}

export type Board = Cell[][];

export interface GameState {
  board: Board;
  currentPiece: Piece | null;
  nextPiece: Piece | null;
  score: number;
  level: number;
  lines: number;
  gameOver: boolean;
  paused: boolean;
}

export interface GameStats {
  score: number;
  level: number;
  lines: number;
  time: number;
}
