'use client';

import React from 'react';
import { Board as BoardType, Piece } from '@/types/tetris';
import { PIECE_SHAPES } from '@/constants/tetris';

interface BoardProps {
  board: BoardType;
  currentPiece: Piece | null;
  ghostPiece?: Piece | null;
}

export default function Board({ board, currentPiece, ghostPiece }: BoardProps) {
  // Create a display board that includes the current piece
  const displayBoard = board.map(row => row.map(cell => ({ ...cell })));
  
  // Add ghost piece to display board
  if (ghostPiece) {
    const shape = PIECE_SHAPES[ghostPiece.type][ghostPiece.rotation];
    for (let y = 0; y < shape.length; y++) {
      for (let x = 0; x < shape[y].length; x++) {
        if (shape[y][x]) {
          const boardX = ghostPiece.position.x + x;
          const boardY = ghostPiece.position.y + y;
          
          if (boardY >= 0 && boardY < displayBoard.length && 
              boardX >= 0 && boardX < displayBoard[0].length &&
              !displayBoard[boardY][boardX].filled) {
            displayBoard[boardY][boardX] = {
              filled: true,
              type: ghostPiece.type,
              isGhost: true
            };
          }
        }
      }
    }
  }
  
  // Add current piece to display board
  if (currentPiece) {
    const shape = PIECE_SHAPES[currentPiece.type][currentPiece.rotation];
    for (let y = 0; y < shape.length; y++) {
      for (let x = 0; x < shape[y].length; x++) {
        if (shape[y][x]) {
          const boardX = currentPiece.position.x + x;
          const boardY = currentPiece.position.y + y;
          
          if (boardY >= 0 && boardY < displayBoard.length && 
              boardX >= 0 && boardX < displayBoard[0].length) {
            displayBoard[boardY][boardX] = {
              filled: true,
              type: currentPiece.type
            };
          }
        }
      }
    }
  }

  return (
    <div className="tetris-board">
      {displayBoard.map((row, y) =>
        row.map((cell, x) => (
          <div
            key={`${x}-${y}`}
            className={`tetris-cell ${
              cell.filled
                ? `tetris-cell-filled ${
                    (cell as any).isGhost
                      ? `tetris-piece-${cell.type?.toLowerCase()} opacity-40 border-dashed`
                      : `tetris-piece-${cell.type?.toLowerCase()}`
                  }`
                : ''
            }`}
          />
        ))
      )}
    </div>
  );
}
