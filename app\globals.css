@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-900 text-white min-h-screen;
  }
}

@layer components {
  .tetris-cell {
    @apply w-6 h-6 border border-gray-600 flex items-center justify-center;
  }
  
  .tetris-cell-filled {
    @apply border-2 border-white;
    transition: all 0.1s ease;
  }

  .tetris-cell-filled:not(.opacity-40) {
    filter: brightness(1.1) saturate(1.2);
  }
  
  .tetris-board {
    @apply grid grid-cols-10 gap-0 border-2 border-gray-400 bg-black p-1;
  }
  
  .tetris-piece-i {
    @apply bg-tetris-cyan;
    background: linear-gradient(135deg, #00FFFF 0%, #00CCCC 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-o {
    @apply bg-tetris-yellow;
    background: linear-gradient(135deg, #FFFF00 0%, #CCCC00 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-t {
    @apply bg-tetris-purple;
    background: linear-gradient(135deg, #9900FF 0%, #7700CC 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-s {
    @apply bg-tetris-green;
    background: linear-gradient(135deg, #00FF66 0%, #00CC44 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-z {
    @apply bg-tetris-red;
    background: linear-gradient(135deg, #FF3333 0%, #CC2222 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-j {
    @apply bg-tetris-blue;
    background: linear-gradient(135deg, #0066FF 0%, #0044CC 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }

  .tetris-piece-l {
    @apply bg-tetris-orange;
    background: linear-gradient(135deg, #FF6600 0%, #CC4400 100%);
    box-shadow: inset 2px 2px 4px rgba(255,255,255,0.3), inset -2px -2px 4px rgba(0,0,0,0.3);
  }
  
  .game-button {
    @apply px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-all duration-200;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateY(0);
  }

  .game-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  .game-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
  }

  .game-button:disabled {
    @apply bg-gray-600 cursor-not-allowed;
    opacity: 0.6;
  }
}
